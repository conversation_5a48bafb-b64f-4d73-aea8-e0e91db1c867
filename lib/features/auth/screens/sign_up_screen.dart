import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/auth_providers.dart';
import '../../../core/widgets/buttons/custom_button.dart';
import '../../../core/widgets/feedback/error_display.dart';
import '../../../core/widgets/forms/custom_text_field.dart';
import '../../../core/widgets/overlays/loading_overlay.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_footer.dart';

/// Sign up screen for user registration
class SignUpScreen extends ConsumerStatefulWidget {
  const SignUpScreen({super.key});

  @override
  ConsumerState<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends ConsumerState<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  bool _isFormValid = false;
  String? _nameError;
  String? _emailError;
  String? _passwordError;
  String? _confirmPasswordError;

  @override
  void initState() {
    super.initState();
    _nameController.addListener(_validateForm);
    _emailController.addListener(_validateForm);
    _passwordController.addListener(_validateForm);
    _confirmPasswordController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  void _validateForm() {
    final name = _nameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    setState(() {
      _nameError = _validateName(name);
      _emailError = _validateEmail(email);
      _passwordError = _validatePassword(password);
      _confirmPasswordError = _validateConfirmPassword(
        password,
        confirmPassword,
      );

      _isFormValid =
          _nameError == null &&
          _emailError == null &&
          _passwordError == null &&
          _confirmPasswordError == null &&
          name.isNotEmpty &&
          email.isNotEmpty &&
          password.isNotEmpty &&
          confirmPassword.isNotEmpty;
    });
  }

  String? _validateName(String name) {
    if (name.isEmpty) {
      return 'Name is required';
    }

    if (name.length < 2) {
      return 'Name must be at least 2 characters';
    }

    return null;
  }

  String? _validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  String? _validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }

    // Check for at least one letter and one number
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(password)) {
      return 'Password must contain at least one letter and one number';
    }

    return null;
  }

  String? _validateConfirmPassword(String password, String confirmPassword) {
    if (confirmPassword.isEmpty) {
      return 'Please confirm your password';
    }

    if (password != confirmPassword) {
      return 'Passwords do not match';
    }

    return null;
  }

  Future<void> _handleSignUp() async {
    if (!_isFormValid) return;

    // Clear any previous errors
    ref.read(authStateProvider.notifier).clearError();

    try {
      await ref
          .read(authStateProvider.notifier)
          .signUpWithEmailAndPassword(
            email: _emailController.text.trim(),
            password: _passwordController.text,
            displayName: _nameController.text.trim(),
          );

      // Navigation will be handled by the router based on auth state
    } catch (e) {
      // Error handling is managed by the auth provider
      // The UI will react to the error state automatically
    }
  }

  void _handleSignIn() {
    context.pop(); // Go back to login screen
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isLoading = ref.watch(isAuthLoadingProvider);
    final authError = ref.watch(authErrorProvider);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: colorScheme.onSurface),
          onPressed: isLoading ? null : () => context.pop(),
        ),
      ),
      body: LoadingOverlay(
        isLoading: isLoading,
        message: 'Creating account...',
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 20.h),

                  // Header
                  const AuthHeader(
                    title: 'Create Account',
                    subtitle: 'Sign up to get started with Scholara',
                    showLogo: false,
                  ),

                  SizedBox(height: 40.h),

                  // Error display
                  if (authError != null) ...[
                    ErrorDisplay.inline(
                      message: authError.message,
                      onDismiss: () {
                        ref.read(authStateProvider.notifier).clearError();
                      },
                    ),
                    SizedBox(height: 24.h),
                  ],

                  // Name field
                  CustomTextField(
                    controller: _nameController,
                    focusNode: _nameFocusNode,
                    labelText: 'Full Name',
                    hintText: 'Enter your full name',
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    errorText: _nameError,
                    isRequired: true,
                    enabled: !isLoading,
                    prefixIcon: Icon(
                      Icons.person_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      _emailFocusNode.requestFocus();
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Email field
                  CustomTextField(
                    controller: _emailController,
                    focusNode: _emailFocusNode,
                    labelText: 'Email',
                    hintText: 'Enter your email address',
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    errorText: _emailError,
                    isRequired: true,
                    enabled: !isLoading,
                    prefixIcon: Icon(
                      Icons.email_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      _passwordFocusNode.requestFocus();
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Password field
                  CustomTextField(
                    controller: _passwordController,
                    focusNode: _passwordFocusNode,
                    labelText: 'Password',
                    hintText: 'Create a strong password',
                    isPassword: true,
                    textInputAction: TextInputAction.next,
                    errorText: _passwordError,
                    isRequired: true,
                    enabled: !isLoading,
                    helperText:
                        'Must be at least 6 characters with letters and numbers',
                    prefixIcon: Icon(
                      Icons.lock_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      _confirmPasswordFocusNode.requestFocus();
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Confirm password field
                  CustomTextField(
                    controller: _confirmPasswordController,
                    focusNode: _confirmPasswordFocusNode,
                    labelText: 'Confirm Password',
                    hintText: 'Re-enter your password',
                    isPassword: true,
                    textInputAction: TextInputAction.done,
                    errorText: _confirmPasswordError,
                    isRequired: true,
                    enabled: !isLoading,
                    prefixIcon: Icon(
                      Icons.lock_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      if (_isFormValid) {
                        _handleSignUp();
                      }
                    },
                  ),

                  SizedBox(height: 32.h),

                  // Sign up button
                  CustomButton.primary(
                    text: 'Create Account',
                    onPressed: _isFormValid ? _handleSignUp : null,
                    isLoading: isLoading,
                    enabled: _isFormValid && !isLoading,
                    width: double.infinity,
                    size: CustomButtonSize.large,
                  ),

                  SizedBox(height: 40.h),

                  // Footer with sign in link
                  AuthFooter(
                    text: 'Already have an account?',
                    actionText: 'Sign In',
                    onActionPressed: isLoading ? null : _handleSignIn,
                  ),

                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
