import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/auth_providers.dart';
import '../../../core/widgets/buttons/custom_button.dart';
import '../../../core/widgets/feedback/error_display.dart';
import '../../../core/widgets/forms/custom_text_field.dart';
import '../../../core/widgets/overlays/loading_overlay.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_footer.dart';

/// Login screen for user authentication
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isFormValid = false;
  String? _emailError;
  String? _passwordError;

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateForm);
    _passwordController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _validateForm() {
    final email = _emailController.text.trim();
    final password = _passwordController.text;

    setState(() {
      _emailError = _validateEmail(email);
      _passwordError = _validatePassword(password);
      _isFormValid =
          _emailError == null &&
          _passwordError == null &&
          email.isNotEmpty &&
          password.isNotEmpty;
    });
  }

  String? _validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  String? _validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }

    return null;
  }

  Future<void> _handleLogin() async {
    if (!_isFormValid) return;

    // Clear any previous errors
    ref.read(authStateProvider.notifier).clearError();

    try {
      await ref
          .read(authStateProvider.notifier)
          .signInWithEmailAndPassword(
            email: _emailController.text.trim(),
            password: _passwordController.text,
          );

      // Navigation will be handled by the router based on auth state
    } catch (e) {
      // Error handling is managed by the auth provider
      // The UI will react to the error state automatically
    }
  }

  void _handleForgotPassword() {
    // TODO: Navigate to forgot password screen or show dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Forgot password feature coming soon')),
    );
  }

  void _handleSignUp() {
    context.pushNamed('signUp');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isLoading = ref.watch(isAuthLoadingProvider);
    final authError = ref.watch(authErrorProvider);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: LoadingOverlay(
        isLoading: isLoading,
        message: 'Signing in...',
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 40.h),

                  // Header
                  const AuthHeader(
                    title: 'Welcome Back',
                    subtitle: 'Sign in to your account to continue',
                  ),

                  SizedBox(height: 40.h),

                  // Error display
                  if (authError != null) ...[
                    ErrorDisplay.inline(
                      message: authError.message,
                      onDismiss: () {
                        ref.read(authStateProvider.notifier).clearError();
                      },
                    ),
                    SizedBox(height: 24.h),
                  ],

                  // Email field
                  CustomTextField(
                    controller: _emailController,
                    focusNode: _emailFocusNode,
                    labelText: 'Email',
                    hintText: 'Enter your email address',
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    errorText: _emailError,
                    isRequired: true,
                    enabled: !isLoading,
                    prefixIcon: Icon(
                      Icons.email_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      _passwordFocusNode.requestFocus();
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Password field
                  CustomTextField(
                    controller: _passwordController,
                    focusNode: _passwordFocusNode,
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    isPassword: true,
                    textInputAction: TextInputAction.done,
                    errorText: _passwordError,
                    isRequired: true,
                    enabled: !isLoading,
                    prefixIcon: Icon(
                      Icons.lock_outlined,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onSubmitted: (_) {
                      if (_isFormValid) {
                        _handleLogin();
                      }
                    },
                  ),

                  SizedBox(height: 16.h),

                  // Forgot password link
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: isLoading ? null : _handleForgotPassword,
                      child: Text(
                        'Forgot Password?',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // Login button
                  CustomButton.primary(
                    text: 'Sign In',
                    onPressed: _isFormValid ? _handleLogin : null,
                    isLoading: isLoading,
                    enabled: _isFormValid && !isLoading,
                    width: double.infinity,
                    size: CustomButtonSize.large,
                  ),

                  SizedBox(height: 40.h),

                  // Footer with sign up link
                  AuthFooter(
                    text: "Don't have an account?",
                    actionText: 'Sign Up',
                    onActionPressed: isLoading ? null : _handleSignUp,
                  ),

                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
