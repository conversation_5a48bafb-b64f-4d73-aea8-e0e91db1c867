import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_providers.dart';
import '../widgets/overlays/loading_overlay.dart';
import 'app_routes.dart';

/// Route guard that protects routes based on authentication state
class RouteGuard {
  /// Check if user is authenticated and redirect if necessary
  static String? redirectLogic(BuildContext context, GoRouterState state, WidgetRef ref) {
    final authState = ref.read(authStateProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    final isLoading = ref.read(isAuthLoadingProvider);
    
    // If auth is still loading, don't redirect yet
    if (isLoading) {
      return null;
    }
    
    final isAuthRoute = _isAuthRoute(state.matchedLocation);
    
    // If user is authenticated and trying to access auth routes, redirect to home
    if (isAuthenticated && isAuthRoute) {
      return AppRoutes.home;
    }
    
    // If user is not authenticated and trying to access protected routes, redirect to login
    if (!isAuthenticated && !isAuthRoute) {
      return AppRoutes.login;
    }
    
    // No redirect needed
    return null;
  }
  
  /// Check if the current route is an authentication route
  static bool _isAuthRoute(String location) {
    return location == AppRoutes.login ||
           location == AppRoutes.signUp ||
           location == AppRoutes.forgotPassword;
  }
  
  /// Check if the current route is a protected route
  static bool _isProtectedRoute(String location) {
    return !_isAuthRoute(location);
  }
}

/// Widget that shows loading screen while auth state is being determined
class AuthLoadingScreen extends ConsumerWidget {
  const AuthLoadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

/// Widget wrapper that handles authentication state
class AuthStateWrapper extends ConsumerWidget {
  final Widget child;
  final String currentRoute;

  const AuthStateWrapper({
    super.key,
    required this.child,
    required this.currentRoute,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final isLoading = ref.watch(isAuthLoadingProvider);
    
    return authState.when(
      data: (state) {
        // If loading, show loading overlay
        if (isLoading) {
          return LoadingOverlay(
            isLoading: true,
            message: 'Loading...',
            child: child,
          );
        }
        
        return child;
      },
      loading: () => const AuthLoadingScreen(),
      error: (error, stackTrace) {
        // If there's an auth error, still show the child
        // The individual screens will handle error display
        return child;
      },
    );
  }
}
