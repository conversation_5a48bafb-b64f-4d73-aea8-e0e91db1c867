import 'package:go_router/go_router.dart';
import 'package:scholara_student/features/auth/screens/login_screen.dart';
import 'package:scholara_student/features/auth/screens/sign_up_screen.dart';
import 'package:scholara_student/features/dashboard/screens/dashboard_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_list_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_detail_screen.dart';
import 'package:scholara_student/features/homework/screens/submit_homework_screen.dart';
import 'package:scholara_student/features/homework/screens/view_submission_screen.dart';
import 'app_routes.dart';

// Main app router configuration
final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login,
  routes: [
    // Authentication routes
    GoRoute(
      path: AppRoutes.login,
      name: RouteNames.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: AppRoutes.signUp,
      name: RouteNames.signUp,
      builder: (context, state) => const SignUpScreen(),
    ),

    // Main app routes
    GoRoute(
      path: AppRoutes.home,
      name: RouteNames.home,
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: AppRoutes.homeworkList,
      name: RouteNames.homeworkList,
      builder: (context, state) => const HomeworkListScreen(),
    ),
    GoRoute(
      path: AppRoutes.homeworkDetail,
      name: RouteNames.homeworkDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return HomeworkDetailScreen(homeworkId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.submitHomework,
      name: RouteNames.submitHomework,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return SubmitHomeworkScreen(homeworkId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.viewSubmission,
      name: RouteNames.viewSubmission,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ViewSubmissionScreen(homeworkId: id);
      },
    ),
  ],
);



// context.pushNamed() -->	Navigate forward (adds to stack)
// context.goNamed() -->	Navigate and clear previous stack
// pathParameters	--> For dynamic routes like /homework/:id
// initialLocation -->	Define default route on app start
// GoRouterRefreshStream -->	Auto-redirect when auth state changes (advanced)

