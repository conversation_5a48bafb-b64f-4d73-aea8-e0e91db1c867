import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Custom button widget with consistent styling and loading states
class CustomButton extends StatelessWidget {
  /// Text to display on the button
  final String text;
  
  /// Callback when button is pressed
  final VoidCallback? onPressed;
  
  /// Whether the button is in loading state
  final bool isLoading;
  
  /// Whether the button is enabled
  final bool enabled;
  
  /// Button type/style
  final CustomButtonType type;
  
  /// Button size
  final CustomButtonSize size;
  
  /// Icon to display before text
  final Widget? icon;
  
  /// Width of the button (null for auto width)
  final double? width;
  
  /// Height of the button (null for default height)
  final double? height;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Custom text color
  final Color? textColor;
  
  /// Custom border color
  final Color? borderColor;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.type = CustomButtonType.primary,
    this.size = CustomButtonSize.medium,
    this.icon,
    this.width,
    this.height,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  });

  /// Create a primary button
  const CustomButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.size = CustomButtonSize.medium,
    this.icon,
    this.width,
    this.height,
  }) : type = CustomButtonType.primary,
       backgroundColor = null,
       textColor = null,
       borderColor = null;

  /// Create a secondary button
  const CustomButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.size = CustomButtonSize.medium,
    this.icon,
    this.width,
    this.height,
  }) : type = CustomButtonType.secondary,
       backgroundColor = null,
       textColor = null,
       borderColor = null;

  /// Create an outline button
  const CustomButton.outline({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.size = CustomButtonSize.medium,
    this.icon,
    this.width,
    this.height,
  }) : type = CustomButtonType.outline,
       backgroundColor = null,
       textColor = null,
       borderColor = null;

  /// Create a text button
  const CustomButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.size = CustomButtonSize.medium,
    this.icon,
    this.width,
    this.height,
  }) : type = CustomButtonType.text,
       backgroundColor = null,
       textColor = null,
       borderColor = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final isButtonEnabled = enabled && !isLoading && onPressed != null;
    
    // Get button dimensions based on size
    final buttonHeight = height ?? _getButtonHeight();
    final buttonPadding = _getButtonPadding();
    final textStyle = _getTextStyle(theme);
    final borderRadius = BorderRadius.circular(12.r);
    
    Widget buttonChild = _buildButtonContent(theme);
    
    switch (type) {
      case CustomButtonType.primary:
        return SizedBox(
          width: width,
          height: buttonHeight,
          child: ElevatedButton(
            onPressed: isButtonEnabled ? onPressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? colorScheme.primary,
              foregroundColor: textColor ?? colorScheme.onPrimary,
              disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
              disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
              padding: buttonPadding,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              elevation: 0,
              shadowColor: Colors.transparent,
            ),
            child: buttonChild,
          ),
        );
        
      case CustomButtonType.secondary:
        return SizedBox(
          width: width,
          height: buttonHeight,
          child: ElevatedButton(
            onPressed: isButtonEnabled ? onPressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? colorScheme.secondary,
              foregroundColor: textColor ?? colorScheme.onSecondary,
              disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
              disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
              padding: buttonPadding,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              elevation: 0,
              shadowColor: Colors.transparent,
            ),
            child: buttonChild,
          ),
        );
        
      case CustomButtonType.outline:
        return SizedBox(
          width: width,
          height: buttonHeight,
          child: OutlinedButton(
            onPressed: isButtonEnabled ? onPressed : null,
            style: OutlinedButton.styleFrom(
              foregroundColor: textColor ?? colorScheme.primary,
              disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
              padding: buttonPadding,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              side: BorderSide(
                color: borderColor ?? (isButtonEnabled 
                    ? colorScheme.primary 
                    : colorScheme.onSurface.withValues(alpha: 0.12)),
                width: 1.5,
              ),
            ),
            child: buttonChild,
          ),
        );
        
      case CustomButtonType.text:
        return SizedBox(
          width: width,
          height: buttonHeight,
          child: TextButton(
            onPressed: isButtonEnabled ? onPressed : null,
            style: TextButton.styleFrom(
              foregroundColor: textColor ?? colorScheme.primary,
              disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
              padding: buttonPadding,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
            ),
            child: buttonChild,
          ),
        );
    }
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16.w,
            height: 16.w,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == CustomButtonType.outline || type == CustomButtonType.text
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onPrimary,
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            'Loading...',
            style: _getTextStyle(theme),
          ),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          SizedBox(width: 8.w),
          Text(
            text,
            style: _getTextStyle(theme),
          ),
        ],
      );
    }

    return Text(
      text,
      style: _getTextStyle(theme),
    );
  }

  double _getButtonHeight() {
    switch (size) {
      case CustomButtonSize.small:
        return 36.h;
      case CustomButtonSize.medium:
        return 48.h;
      case CustomButtonSize.large:
        return 56.h;
    }
  }

  EdgeInsets _getButtonPadding() {
    switch (size) {
      case CustomButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h);
      case CustomButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h);
      case CustomButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h);
    }
  }

  TextStyle? _getTextStyle(ThemeData theme) {
    switch (size) {
      case CustomButtonSize.small:
        return theme.textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.w600,
        );
      case CustomButtonSize.medium:
        return theme.textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        );
      case CustomButtonSize.large:
        return theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        );
    }
  }
}

/// Button type enum
enum CustomButtonType {
  primary,
  secondary,
  outline,
  text,
}

/// Button size enum
enum CustomButtonSize {
  small,
  medium,
  large,
}
